-- Create form_field_configs table
CREATE TABLE IF NOT EXISTS form_field_configs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    title_el TEXT NOT NULL,
    title_en TEXT NOT NULL,
    placeholder_el TEXT,
    placeholder_en TEXT,
    description_el TEXT,
    description_en TEXT,
    field_type VARCHAR(50) NOT NULL CHECK (field_type IN ('select', 'multiselect', 'checkbox', 'radio')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create form_field_options table
CREATE TABLE IF NOT EXISTS form_field_options (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    field_config_id UUID NOT NULL REFERENCES form_field_configs(id) ON DELETE CASCADE,
    label_el TEXT NOT NULL,
    label_en TEXT NOT NULL,
    order_index INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_form_field_configs_name ON form_field_configs(name);
CREATE INDEX IF NOT EXISTS idx_form_field_configs_is_active ON form_field_configs(is_active);
CREATE INDEX IF NOT EXISTS idx_form_field_options_field_config_id ON form_field_options(field_config_id);
CREATE INDEX IF NOT EXISTS idx_form_field_options_order_index ON form_field_options(order_index);
CREATE INDEX IF NOT EXISTS idx_form_field_options_is_active ON form_field_options(is_active);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_form_field_configs_updated_at 
    BEFORE UPDATE ON form_field_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_form_field_options_updated_at 
    BEFORE UPDATE ON form_field_options 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial form configurations based on existing hardcoded options
INSERT INTO form_field_configs (name, title_el, title_en, field_type, is_active) VALUES
('musicalInstruments', 'Μουσικά Όργανα', 'Musical Instruments', 'multiselect', true),
('musicTheory', 'Θεωρία Μουσικής', 'Music Theory', 'multiselect', true),
('lessonFormat', 'Μορφή Μαθημάτων', 'Lesson Format', 'multiselect', true),
('education', 'Εκπαίδευση', 'Education', 'multiselect', true),
('specialization', 'Ειδικότητες', 'Specializations', 'multiselect', true),
('firstAid', 'Πρώτες Βοήθειες', 'First Aid', 'multiselect', true),
('childrenAge', 'Ηλικίες Παιδιών', 'Children Ages', 'multiselect', true),
('position', 'Θέσεις Εργασίας', 'Job Positions', 'multiselect', true),
('schedule', 'Ωράρια', 'Schedules', 'multiselect', true),
('duration', 'Διάρκεια', 'Duration', 'multiselect', true),
('candidateType', 'Τύπος Υποψηφίου', 'Candidate Type', 'radio', true),
('startDate', 'Ημερομηνία Έναρξης', 'Start Date', 'radio', true),
('socialMediaPlatform', 'Πλατφόρμες Κοινωνικής Δικτύωσης', 'Social Media Platforms', 'select', true);

-- Insert musical instrument options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Πιάνο', 'Piano', 0, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Αρμόνιο', 'Harmonium', 1, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Κιθάρα', 'Guitar', 2, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Φλογέρα', 'Recorder', 3, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Φλάουτο', 'Flute', 4, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Βιολί', 'Violin', 5, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Βιολοντσέλο', 'Cello', 6, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Σαξόφωνο Άλτο', 'Alto Saxophone', 7, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Μπουζούκι', 'Bouzouki', 8, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Παραδοσιακά Κρουστά', 'Traditional Percussion', 9, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicalInstruments'),
    'Φωνητική', 'Vocals', 10, true;

-- Insert music theory options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicTheory'),
    'Θεωρία', 'Theory', 0, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicTheory'),
    'Αρμονία', 'Harmony', 1, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicTheory'),
    'Αντίστιξη', 'Counterpoint', 2, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicTheory'),
    'Φούγκα', 'Fugue', 3, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicTheory'),
    'Σύνθεση', 'Composition', 4, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicTheory'),
    'Jazz Θεωρία', 'Jazz Theory', 5, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'musicTheory'),
    'Βυζαντινή Μουσική', 'Byzantine Music', 6, true;

-- Insert lesson format options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'lessonFormat'),
    'Δια ζώσης', 'Live', 0, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'lessonFormat'),
    'Διαδικτυακά', 'Online', 1, true;

-- Insert first aid options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'firstAid'),
    'Για βρέφη', 'For infants', 0, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'firstAid'),
    'Για παιδιά', 'For children', 1, true
UNION ALL SELECT 
    (SELECT id FROM form_field_configs WHERE name = 'firstAid'),
    'Για ενήλικες', 'For adults', 2, true;

-- Insert candidate type options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT
    (SELECT id FROM form_field_configs WHERE name = 'candidateType'),
    'Nanny', 'Nanny', 0, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'candidateType'),
    'Tutor', 'Tutor', 1, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'candidateType'),
    'Και τα δύο', 'Both', 2, true;

-- Insert education options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT
    (SELECT id FROM form_field_configs WHERE name = 'education'),
    'Απολυτήριο Λυκείου', 'High School Certificate', 0, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'education'),
    'Τ.Ε.Ι.', 'Technological Education Institute', 1, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'education'),
    'Α.Ε.Ι.', 'College', 2, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'education'),
    'Ι.Ε.Κ.', 'Institute of Vocational Training', 3, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'education'),
    'Πιστοποιημένο Πτυχίο', 'Associates Degree', 4, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'education'),
    'Πτυχίο Bachelor', 'Bachelor''s Degree', 5, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'education'),
    'Μεταπτυχιακό Δίπλωμα', 'Master of Science or Master''s Degree', 6, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'education'),
    'Διδακτορικό Δίπλωμα', 'PhD', 7, true;

-- Insert children age options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT
    (SELECT id FROM form_field_configs WHERE name = 'childrenAge'),
    'Νεογέννητα (Ημέρα 1-3 μηνών)', 'Newborns (Day 1- 3 months old)', 0, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'childrenAge'),
    'Βρέφη (3-9 μηνών)', 'Infants (3-9 months old)', 1, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'childrenAge'),
    'Βρέφη (9-18 μηνών)', 'Infants (9-18 months old)', 2, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'childrenAge'),
    'Νήπια (18 μηνών-3 ετών)', 'Toddlers (18 months-3 years old)', 3, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'childrenAge'),
    'Προσχολική (3-6 ετών)', 'Preschool (3-6 years old)', 4, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'childrenAge'),
    'Δημοτικό (7-12 ετών)', 'Primary school (7-12 years old)', 5, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'childrenAge'),
    'Εφηβεία (13+ ετών)', 'Teenagers (13+years old)', 6, true;

-- Insert position options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT
    (SELECT id FROM form_field_configs WHERE name = 'position'),
    'Nanny', 'Nanny', 0, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'position'),
    'Maternity/Night Nanny', 'Maternity/Night Nanny', 1, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'position'),
    'Travel/Vacation Nanny', 'Travel/Vacation Nanny', 2, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'position'),
    'Hotel Nanny', 'Hotel Nanny', 3, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'position'),
    'Yacht Nanny', 'Yacht Nanny', 4, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'position'),
    'Event Nanny', 'Event Nanny', 5, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'position'),
    'Weekend Nanny', 'Weekend Nanny', 6, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'position'),
    'Baby-Sitting', 'Baby-Sitting', 7, true;

-- Insert schedule options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT
    (SELECT id FROM form_field_configs WHERE name = 'schedule'),
    'Full-Time', 'Full-Time', 0, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'schedule'),
    'Part-Time', 'Part-Time', 1, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'schedule'),
    'Flexible', 'Flexible', 2, true;

-- Insert duration options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT
    (SELECT id FROM form_field_configs WHERE name = 'duration'),
    'Long-Term', 'Long-Term', 0, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'duration'),
    'Short-Term', 'Short-Term', 1, true;

-- Insert start date options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT
    (SELECT id FROM form_field_configs WHERE name = 'startDate'),
    'Άμεσα', 'Immediately', 0, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'startDate'),
    'Σε λίγες εβδομάδες', 'In a few weeks', 1, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'startDate'),
    'Ευέλικτα', 'Flexible', 2, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'startDate'),
    'Άλλο', 'Other', 3, true;

-- Insert social media platform options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT
    (SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'),
    'Facebook', 'Facebook', 0, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'),
    'Instagram', 'Instagram', 1, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'),
    'LinkedIn', 'LinkedIn', 2, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'),
    'Twitter', 'Twitter', 3, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'),
    'TikTok', 'TikTok', 4, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'),
    'Άλλο', 'Other', 5, true;

-- Insert specialization options (first batch)
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active)
SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Μαία', 'Maternity Nurse', 0, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Maternity Nanny', 'Maternity Nanny', 1, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Βοηθός Βρεφονηπιοκόμου', 'Nursery Childcare Assistant', 2, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Βρεφονηπιοκόμος', 'Nursery Childcare', 3, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Νηπιαγωγός', 'Kindergarten Teacher', 4, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Δημοτική Εκπαίδευση', 'Primary School Teacher', 5, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Παιδαγωγικά (Γενικά)', 'Other Pedagogy Certification', 6, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Ξένες Γλώσσες', 'Foreign Languages', 7, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Νοηματική Γλώσσα', 'Sign Language', 8, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Γραφή Braille', 'Braille System', 9, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Ειδικής Αγωγής', 'Special Needs Education', 10, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Εργοθεραπεία', 'Occupational Therapy', 11, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Λογοθεραπεία', 'Speech Therapy', 12, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Μουσικοπαιδαγωγικά', 'Music Pedagogy', 13, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Μουσικοθεραπεία', 'Music Therapy', 14, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Μουσικό Όργανο', 'Musical Instrument', 15, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Θεωρητικά της Μουσικής', 'Music Theory', 16, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Θεατροπαιδαγωγικά', 'Theatre Pedagogy', 17, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Δραματοθεραπεία', 'Drama Therapy', 18, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Παιγνιοθεραπεία', 'Play Therapy', 19, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Εικαστικές Τέχνες', 'Visual Arts', 20, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Χορός-Κινησιολογία', 'Dance-Kinesiology', 21, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Χοροθεραπεία-Χοροκινητική Ψυχοθεραπεία', 'Dance-Movement Therapy', 22, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Ψυχολογία', 'Psychology', 23, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Παιδοψυχολογία', 'Child Psychology', 24, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Παιδοψυχιατρική', 'Child Psychiatry', 25, true
UNION ALL SELECT
    (SELECT id FROM form_field_configs WHERE name = 'specialization'),
    'Κάτι άλλο…', 'Other…', 26, true;

-- Enable Row Level Security (RLS)
ALTER TABLE form_field_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_field_options ENABLE ROW LEVEL SECURITY;

-- Create policies for form_field_configs
CREATE POLICY "Allow read access to form_field_configs" ON form_field_configs
    FOR SELECT USING (true);

CREATE POLICY "Allow insert access to form_field_configs for authenticated users" ON form_field_configs
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow update access to form_field_configs for authenticated users" ON form_field_configs
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow delete access to form_field_configs for authenticated users" ON form_field_configs
    FOR DELETE USING (auth.role() = 'authenticated');

-- Create policies for form_field_options
CREATE POLICY "Allow read access to form_field_options" ON form_field_options
    FOR SELECT USING (true);

CREATE POLICY "Allow insert access to form_field_options for authenticated users" ON form_field_options
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow update access to form_field_options for authenticated users" ON form_field_options
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow delete access to form_field_options for authenticated users" ON form_field_options
    FOR DELETE USING (auth.role() = 'authenticated');

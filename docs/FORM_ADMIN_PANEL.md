# Form Administration Panel

The Form Administration Panel allows administrators to manage form field configurations, their options, order, and multilingual text content without requiring code changes.

## Features

### 🎛️ Form Field Configuration Management
- Create, edit, and delete form field configurations
- Support for different field types: select, multiselect, checkbox, radio
- Multilingual titles, placeholders, and descriptions (Greek/English)
- Active/inactive status management

### 📝 Option Management
- Add, edit, and delete options for each form field
- Drag-and-drop reordering of options
- Multilingual labels (Greek/English)
- Individual option activation/deactivation

### 🌐 Multilingual Support
- Full Greek and English language support
- Separate labels for each language
- Consistent text management across the application

### 🔄 Dynamic Form Loading
- Forms automatically load options from the database
- Fallback to hardcoded options for reliability
- Real-time updates without application restart

## Access

The Form Administration Panel is accessible through:
1. Navigate to **Settings** (Ρυθμίσεις)
2. Click on the **Forms** (Φόρμες) tab
3. Requires authenticated user access

## Database Schema

### Tables Created

#### `form_field_configs`
Stores form field configuration metadata:
- `id` - Unique identifier
- `name` - Internal field name (e.g., 'musicalInstruments')
- `title_el` - Greek title
- `title_en` - English title
- `placeholder_el` - Greek placeholder text
- `placeholder_en` - English placeholder text
- `description_el` - Greek description
- `description_en` - English description
- `field_type` - Type: select, multiselect, checkbox, radio
- `is_active` - Active status
- `created_at` / `updated_at` - Timestamps

#### `form_field_options`
Stores individual options for each form field:
- `id` - Unique identifier
- `field_config_id` - Reference to form_field_configs
- `label_el` - Greek label
- `label_en` - English label
- `order_index` - Display order
- `is_active` - Active status
- `created_at` / `updated_at` - Timestamps

## Pre-configured Form Fields

The system comes with the following pre-configured form fields:

### Musical & Educational
- **musicalInstruments** - Musical instruments selection
- **musicTheory** - Music theory subjects
- **lessonFormat** - Lesson delivery formats
- **education** - Education levels
- **specialization** - Professional specializations

### Professional & Personal
- **firstAid** - First aid certifications
- **childrenAge** - Children age groups
- **position** - Job positions
- **schedule** - Work schedules
- **duration** - Employment duration
- **candidateType** - Candidate types
- **startDate** - Start date preferences
- **socialMediaPlatform** - Social media platforms

## Usage in Forms

### Dynamic Form Fields
Replace hardcoded form fields with dynamic ones:

```tsx
// Before (hardcoded)
<FormField
  control={control}
  name="musicalInstruments"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Musical Instruments</FormLabel>
      <div className="grid grid-cols-2 gap-2">
        {musicalInstrumentOptions.map((option) => (
          <Checkbox key={option.id} ... />
        ))}
      </div>
    </FormItem>
  )}
/>

// After (dynamic)
<DynamicFormField
  control={control}
  name="musicalInstruments"
  configName="musicalInstruments"
  language="en"
/>
```

### Fallback Support
For reliability, use fallback fields:

```tsx
<FallbackFormField
  control={control}
  name="musicalInstruments"
  title="Musical Instruments"
  options={musicalInstrumentOptions}
  fieldType="multiselect"
  language="en"
/>
```

### Hooks Available

#### `useFormConfigs()`
Load all form configurations:
```tsx
const { configs, loading, error, refetch } = useFormConfigs();
```

#### `useFormConfig(name)`
Load specific form configuration:
```tsx
const { config, loading, error, refetch } = useFormConfig('musicalInstruments');
```

#### `useFormOptions(configName)`
Get formatted options for a form field:
```tsx
const { options, loading, error, config } = useFormOptions('musicalInstruments');
```

## Administration Tasks

### Adding New Form Fields

1. **Create Configuration**:
   - Go to Settings → Forms → Field Configurations
   - Click "Add Configuration"
   - Fill in name, titles, and field type
   - Save the configuration

2. **Add Options**:
   - Click "Manage Options" on the created configuration
   - Add individual options with Greek and English labels
   - Set the display order
   - Activate/deactivate as needed

3. **Update Forms**:
   - Replace hardcoded form fields with `DynamicFormField`
   - Use the configuration name as `configName` prop

### Modifying Existing Options

1. **Reorder Options**:
   - Use the up/down arrows in the options manager
   - Changes are saved automatically

2. **Edit Labels**:
   - Click the edit button on any option
   - Update Greek and English labels
   - Save changes

3. **Add/Remove Options**:
   - Use "Add Option" button to add new choices
   - Use delete button to remove (soft delete)

### Managing Translations

1. **Field Titles**:
   - Edit configuration to update titles
   - Separate fields for Greek and English

2. **Option Labels**:
   - Each option has Greek and English labels
   - Consistent translation management

3. **Placeholders & Descriptions**:
   - Optional placeholder and description text
   - Multilingual support for better UX

## Migration & Data Import

### Initial Setup
The migration script automatically:
- Creates required database tables
- Sets up indexes for performance
- Configures Row Level Security (RLS)
- Imports existing hardcoded options

### Importing Existing Data
Use the "Import Existing Options" button to:
- Migrate hardcoded form options to database
- Preserve existing functionality
- Enable dynamic management

## Security

### Row Level Security (RLS)
- Read access: Public (for form rendering)
- Write access: Authenticated users only
- Admin-level permissions recommended for modifications

### Data Validation
- Server-side validation for all inputs
- Type checking for field types
- Order index validation
- Multilingual content requirements

## Performance Considerations

### Caching
- Form configurations are cached in React state
- Minimal database queries during form rendering
- Efficient loading with proper indexing

### Optimization
- Lazy loading of form configurations
- Debounced search and filtering
- Optimized database queries with indexes

## Troubleshooting

### Common Issues

1. **Options Not Loading**:
   - Check database connection
   - Verify configuration name matches
   - Check RLS policies

2. **Missing Translations**:
   - Ensure both Greek and English labels are provided
   - Check language prop in components

3. **Order Not Updating**:
   - Verify order_index values
   - Check for database transaction issues

### Fallback Behavior
- Dynamic fields automatically fall back to hardcoded options
- Error boundaries prevent form breakage
- Graceful degradation for better UX

## Future Enhancements

### Planned Features
- Bulk import/export of configurations
- Version control for form changes
- Advanced validation rules
- Custom field types
- Form analytics and usage tracking

### Integration Opportunities
- CMS integration for content management
- API endpoints for external systems
- Webhook notifications for changes
- Audit logging for compliance

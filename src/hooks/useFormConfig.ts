import { FormFieldConfig } from "@/schemas/FormSchema";
import { fetchFormConfigByName, fetchFormConfigs } from "@/services/formConfigService";
import { useEffect, useState } from "react";

/**
 * Hook to fetch and manage form configurations
 */
export function useFormConfigs() {
  const [configs, setConfigs] = useState<FormFieldConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadConfigs = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchFormConfigs();
      setConfigs(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load form configurations");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  return {
    configs,
    loading,
    error,
    refetch: loadConfigs,
  };
}

/**
 * Hook to fetch a specific form configuration by name
 */
export function useFormConfig(name: string) {
  const [config, setConfig] = useState<FormFieldConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadConfig = async () => {
    if (!name) return;
    
    setLoading(true);
    setError(null);
    try {
      const data = await fetchFormConfigByName(name);
      setConfig(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load form configuration");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfig();
  }, [name]);

  return {
    config,
    loading,
    error,
    refetch: loadConfig,
  };
}

/**
 * Hook to get form options in the format expected by existing components
 */
export function useFormOptions(configName: string) {
  const { config, loading, error } = useFormConfig(configName);

  // Transform config options to the format expected by existing components
  const options = config?.options.map(option => ({
    id: option.id,
    label: option.labelEn,
    labelEl: option.labelEl,
    labelEn: option.labelEn,
  })) || [];

  return {
    options,
    loading,
    error,
    config,
  };
}

/**
 * Utility function to get localized label from form option
 */
export function getFormOptionLabel(option: { labelEl: string; labelEn: string }, language: "el" | "en" = "en"): string {
  return language === "el" ? option.labelEl : option.labelEn;
}

/**
 * Utility function to get form configuration title
 */
export function getFormConfigTitle(config: FormFieldConfig | null, language: "el" | "en" = "en"): string {
  if (!config) return "";
  return language === "el" ? config.titleEl : config.titleEn;
}

/**
 * Utility function to get form configuration placeholder
 */
export function getFormConfigPlaceholder(config: FormFieldConfig | null, language: "el" | "en" = "en"): string {
  if (!config) return "";
  const placeholder = language === "el" ? config.placeholderEl : config.placeholderEn;
  return placeholder || "";
}

/**
 * Utility function to get form configuration description
 */
export function getFormConfigDescription(config: FormFieldConfig | null, language: "el" | "en" = "en"): string {
  if (!config) return "";
  const description = language === "el" ? config.descriptionEl : config.descriptionEn;
  return description || "";
}

import { Baby, Briefcase, HomeIcon, Mail, Phone, Users } from "lucide-react";
import React from "react";
import { useFormContext as useRHFFormContext } from "react-hook-form";
import { z } from "zod";
import { useNannyRequestFormContext } from "../../../contexts/NannyRequestFormContext";
import { useFormLabels } from "../../../hooks/useFormLabels";
import { useFormConfigs } from "../../../hooks/useFormConfigs";
import { nannyRequestFormSchema } from "../../../schemas/FormSchema";
import { getText } from "../../../utils/form-utils";
import { DynamicFormField } from "../DynamicFormField";
import { Card, CardContent } from "../../ui/card";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../ui/form";
import { Input } from "../../ui/input";
import { Textarea } from "../../ui/textarea";

/**
 * Example component showing how to integrate dynamic form fields and labels
 * This demonstrates the pattern that should be used in all form steps
 */
export const DynamicFormStepExample: React.FC = () => {
  const { language } = useNannyRequestFormContext();
  const form = useRHFFormContext<z.infer<typeof nannyRequestFormSchema>>();
  const { getLabel } = useFormLabels();
  const { getFieldsByCategory } = useFormConfigs();

  // Get dynamic fields for this category
  const personalInfoFields = getFieldsByCategory("personalInfo", "nannyRequest");
  const addressFields = getFieldsByCategory("address", "nannyRequest");
  const childrenFields = getFieldsByCategory("children", "nannyRequest");

  return (
    <>
      {/* Personal Information Section */}
      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-white">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-primary relative">
            <Users className="w-5 h-5 mr-2 text-secondary animate-pulse-soft" />
            {getLabel("personalInfo.title", language, getText("Προσωπικά Στοιχεία / Personal Information", language))}
          </h2>

          <div className="grid gap-6 md:grid-cols-2">
            {/* Render dynamic fields for personal info */}
            {personalInfoFields.map((fieldConfig) => (
              <DynamicFormField
                key={fieldConfig.fieldKey}
                control={form.control}
                name={fieldConfig.fieldKey as any}
                configName={fieldConfig.configName}
                language={language}
                required={fieldConfig.isRequired}
              />
            ))}

            {/* Example of a field with custom icon and styling */}
            <FormField
              control={form.control}
              name="contactNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getLabel("contactNumber.label", language, getText("Τηλέφωνο / Contact Number", language))}
                  </FormLabel>
                  <FormDescription className="text-secondary">
                    {getLabel("contactNumber.description", language, getText("Με κωδικό περιοχής ή χώρας / With Area or Country Code", language))}
                  </FormDescription>
                  <div className="relative">
                    <FormControl>
                      <Input
                        placeholder={getLabel("contactNumber.placeholder", language, "+30 ************")}
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Phone className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />

            {/* Email field with dynamic label and description */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getLabel("email.label", language, "E-mail")}
                  </FormLabel>
                  <FormDescription className="text-secondary">
                    {getLabel("email.description", language, getText("Απαραίτητο , εδώ θα λάβετε τα στοιχεία σας / Required, here you will receive your information", language))}
                  </FormDescription>
                  <div className="relative">
                    <FormControl>
                      <Input
                        placeholder={getLabel("email.placeholder", language, "<EMAIL>")}
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Mail className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Address Section */}
      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-card">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-primary relative">
            <HomeIcon className="w-5 h-5 mr-2 animate-pulse-soft" />
            {getLabel("address.title", language, getText("Διεύθυνση / Address", language))}
          </h2>

          {/* Render dynamic address fields */}
          <div className="space-y-4">
            {addressFields.map((fieldConfig) => (
              <DynamicFormField
                key={fieldConfig.fieldKey}
                control={form.control}
                name={fieldConfig.fieldKey as any}
                configName={fieldConfig.configName}
                language={language}
                required={fieldConfig.isRequired}
              />
            ))}
          </div>

          {/* Example of a custom textarea field with dynamic labels */}
          <FormField
            control={form.control}
            name="permanentAddress"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-primary font-medium">
                  {getLabel("permanentAddress.label", language, getText("Διεύθυνση Μόνιμης Κατοικίας / Address of your Permanent Residence", language))}
                </FormLabel>
                <FormDescription className="text-secondary">
                  {getLabel("permanentAddress.description", language, getText("Διεύθυνση, αριθμός, περιοχή και Τ.Κ. / Address, number, area, postal code etc.", language))}
                </FormDescription>
                <FormControl>
                  <Textarea
                    placeholder={getLabel("permanentAddress.placeholder", language, getText("Οδός Παραδείγματος 123, Αθήνα, 12345 / Example Street 123, Athens, 12345", language))}
                    {...field}
                    className="rounded-lg bg-light/10 border-light/30 focus:border-primary focus:ring-primary min-h-[80px] hover-border-grow"
                  />
                </FormControl>
                <FormMessage className="text-destructive" lang={language} />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Children Information Section */}
      <Card className="border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-gradient-to-br from-white via-white to-accent/20">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-primary relative">
            <Baby className="w-5 h-5 mr-2 animate-pulse-soft" />
            {getLabel("children.title", language, getText("Πληροφορίες Παιδιών / Children Information", language))}
          </h2>

          <div className="space-y-6">
            {/* Render dynamic children fields */}
            {childrenFields.map((fieldConfig) => (
              <DynamicFormField
                key={fieldConfig.fieldKey}
                control={form.control}
                name={fieldConfig.fieldKey as any}
                configName={fieldConfig.configName}
                language={language}
                required={fieldConfig.isRequired}
              />
            ))}

            {/* Example of a number input with dynamic labels */}
            <FormField
              control={form.control}
              name="childrenCount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getLabel("childrenCount.label", language, getText("Πόσα παιδιά αφορά η θέση εργασίας / How many children are you looking for a nanny for?", language))}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      placeholder={getLabel("childrenCount.placeholder", language, "1")}
                      {...field}
                      className="rounded-lg bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />

            {/* Example of a textarea with dynamic labels */}
            <FormField
              control={form.control}
              name="childSpecialInfo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getLabel("childSpecialInfo.label", language, getText("Οποιαδήποτε πληροφορία σχετικά με το παιδί που θα έπρεπε να γνωρίζουμε; / Any special information about the child we should be informed of?", language))}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={getLabel("childSpecialInfo.placeholder", language, getText("Παρακαλώ περιγράψτε οποιαδήποτε ειδική πληροφορία... / Please describe any special information...", language))}
                      {...field}
                      className="rounded-lg bg-light/10 border-light/30 focus:border-primary focus:ring-primary min-h-[100px] hover-border-grow"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Decorative element */}
      <div className="flex justify-center mt-8 mb-4 opacity-70">
        <div className="w-24 h-1 rounded-full bg-gradient-to-r from-primary/70 to-accent-foreground/70 animate-pulse-soft"></div>
      </div>
    </>
  );
};

/**
 * Helper component to render a section with dynamic fields
 */
interface DynamicSectionProps {
  title: string;
  titleKey: string;
  category: string;
  formType: "candidate" | "nannyRequest";
  language: "el" | "en";
  icon?: React.ReactNode;
  className?: string;
  gridCols?: number;
}

export const DynamicSection: React.FC<DynamicSectionProps> = ({
  title,
  titleKey,
  category,
  formType,
  language,
  icon,
  className,
  gridCols = 2,
}) => {
  const form = useRHFFormContext();
  const { getLabel } = useFormLabels();
  const { getFieldsByCategory } = useFormConfigs();

  const fields = getFieldsByCategory(category, formType);

  if (fields.length === 0) {
    return null;
  }

  return (
    <Card className={`mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-white ${className}`}>
      <CardContent className="pt-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center text-primary relative">
          {icon && <span className="w-5 h-5 mr-2 text-secondary animate-pulse-soft">{icon}</span>}
          {getLabel(titleKey, language, title)}
        </h2>

        <div className={`grid gap-6 md:grid-cols-${gridCols}`}>
          {fields.map((fieldConfig) => (
            <DynamicFormField
              key={fieldConfig.fieldKey}
              control={form.control}
              name={fieldConfig.fieldKey as any}
              configName={fieldConfig.configName}
              language={language}
              required={fieldConfig.isRequired}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
